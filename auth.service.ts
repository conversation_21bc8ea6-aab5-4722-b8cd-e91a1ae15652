import { Injectable, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { UserService } from '../user/user.service';
import { PrismaService } from '../prisma/prisma.service';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { SignUpInput } from './dto/sign-up.input';
import { LoginInput } from './dto/login.input';
import { ForgotPasswordInput } from './dto/forgot-password.input';
import { ResetPasswordInput } from './dto/reset-password.input';
import { MailService } from '../mail/mail.service';
import { ConfigService } from '@nestjs/config';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class AuthService {
  constructor(
    private userService: UserService,
    private prisma: PrismaService,
    private jwtService: JwtService,
    private mailService: MailService,
    private configService: ConfigService,
  ) {}

  async signUp(input: SignUpInput) {
    const { email, username, mobileNumber, password, role } = input;

    // Check if user exists
    const existingUser = await this.prisma.user.findFirst({
      where: { OR: [{ email }, { username }, { mobileNumber }] },
    });
    if (existingUser) {
      throw new BadRequestException('User already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create user
    const user = await this.userService.create({
      email,
      username,
      mobileNumber,
      password: hashedPassword,
      role,
    });

    return {
      message: 'Registration successful, redirecting to login page',
      redirectUrl: '/login',
    };
  }

  async login(input: LoginInput) {
    const { identifier, password } = input;

    // Find user by username or mobileNumber
    const user = await this.prisma.user.findFirst({
      where: { OR: [{ username: identifier }, { mobileNumber: identifier }] },
    });

    if (!user || !user.password) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Validate password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Generate tokens
    const accessToken = this.jwtService.sign({ userId: user.id, role: user.role });
    const refreshToken = this.jwtService.sign(
      { userId: user.id, role: user.role },
      { expiresIn: '7d' },
    );

    return {
      accessToken,
      refreshToken,
      user: { id: user.id, role: user.role },
      message: 'Authentication successful, redirecting to dashboard',
      redirectUrl: `/dashboard/${user.role.toLowerCase()}`,
    };
  }

  async forgotPassword(input: ForgotPasswordInput) {
    const { email } = input;

    const user = await this.prisma.user.findUnique({ where: { email } });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    // Generate reset token
    const resetToken = uuidv4();
    const resetTokenExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

    // Save reset token
    await this.prisma.user.update({
      where: { email },
      data: { resetToken, resetTokenExpiry },
    });

    // Send reset email
    const resetUrl = `${this.configService.get<string>('FRONTEND_URL')}/reset-password?token=${resetToken}`;
    await this.mailService.sendPasswordResetEmail(email, resetUrl);

    return { message: 'Reset link sent to your email' };
  }

  async resetPassword(input: ResetPasswordInput) {
    const { token, newPassword } = input;

    const user = await this.prisma.user.findFirst({
      where: { resetToken: token, resetTokenExpiry: { gte: new Date() } },
    });

    if (!user) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update password and clear reset token
    await this.prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        resetToken: null,
        resetTokenExpiry: null,
      },
    });

    return {
      message: 'Password reset successful, redirecting to login page',
      redirectUrl: '/login',
    };
  }

  async validateUser(userId: number) {
    return this.userService.findOne(userId);
  }

  async googleLogin(user: any) {
    if (!user) {
      throw new UnauthorizedException('Google login failed');
    }

    let dbUser = await this.prisma.user.findFirst({
      where { oauthProvider: 'google', oauthId: user.id },
    });

    if (!dbUser) {
      dbUser = await this.userService.create({
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        profilePicture: user.picture,
        oauthProvider: 'google',
        oauthId: user.id,
        role: 'StandardUser', // Default role
      });
    }

    const accessToken = this.jwtService.sign({ userId: dbUser.id, role: dbUser.role });
    const refreshToken = this.jwtService.sign(
      { userId: dbUser.id, role: dbUser.role },
      { expiresIn: '7d' },
    );

    return {
      accessToken,
      refreshToken,
      user: { id: dbUser.id, role: dbUser.role },
      message: 'Google authentication successful',
      redirectUrl: `/dashboard/${dbUser.role.toLowerCase()}`,
    };
  }

  async facebookLogin(user: any) {
    if (!user) {
      throw new UnauthorizedException('Facebook login failed');
    }

    let dbUser = await this.prisma.user.findFirst({
      where: { oauthProvider: 'facebook', oauthId: user.id },
    });

    if (!dbUser) {
      dbUser = await this.userService.create({
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        profilePicture: user.picture,
        oauthProvider: 'facebook',
        oauthId: user.id,
        role: 'StandardUser', // Default role
      });
    }

    const accessToken = this.jwtService.sign({ userId: dbUser.id, role: dbUser.role });
    const refreshToken = this.jwtService.sign(
      { userId: dbUser.id, role: dbUser.role },
      { expiresIn: '7d' },
    );

    return {
      accessToken,
      refreshToken,
      user: { id: dbUser.id, role: dbUser.role },
      message: 'Facebook authentication successful',
      redirectUrl: `/dashboard/${dbUser.role.toLowerCase()}`,
    };
  }
}