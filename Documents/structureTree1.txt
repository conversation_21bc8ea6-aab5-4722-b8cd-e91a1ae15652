.
├── .env
├── docker-compose.yml
├── nginx/
│   └── nginx.conf
├── backend/
│   ├── src/
│   │   ├── auth/
│   │   │   ├── strategies/
│   │   │   │   ├── local.strategy.ts
│   │   │   │   ├── jwt.strategy.ts
│   │   │   │   ├── google.strategy.ts
│   │   │   │   └── facebook.strategy.ts
│   │   │   ├── guards/
│   │   │   │   └── roles.guard.ts
│   │   │   ├── dto/
│   │   │   │   ├── sign-up.input.ts
│   │   │   │   ├── login.input.ts
│   │   │   │   └── reset-password.input.ts
│   │   │   ├── auth.module.ts
│   │   │   ├── auth.resolver.ts
│   │   │   └── auth.service.ts
│   │   ├── user/
│   │   │   ├── user.module.ts
│   │   │   ├── user.resolver.ts
│   │   │   └── user.service.ts
│   │   ├── payment/
│   │   │   ├── stripe/
│   │   │   │   ├── webhook.controller.ts
│   │   │   │   ├── stripe.service.ts
│   │   │   │   └── stripe.module.ts
│   │   │   ├── dto/
│   │   │   │   └── create-session.input.ts
│   │   │   ├── payment.module.ts
│   │   │   ├── payment.resolver.ts
│   │   │   └── payment.service.ts
│   │   ├── mail/
│   │   │   ├── mail.module.ts
│   │   │   └── mail.service.ts
│   │   ├── prisma/
│   │   │   ├── prisma.service.ts
│   │   │   └── prisma.module.ts
│   │   ├── app.module.ts
│   │   └── main.ts
│   ├── prisma/
│   │   └── schema.prisma
│   ├── Dockerfile
│   └── package.json
└── frontend/
    ├── src/
    │   ├── app/
    │   │   ├── (auth)/
    │   │   │   ├── sign-up/
    │   │   │   │   └── page.tsx
    │   │   │   ├── login/
    │   │   │   │   └── page.tsx
    │   │   │   ├── forgot-password/
    │   │   │   │   └── page.tsx
    │   │   │   └── reset-password/
    │   │   │       └── page.tsx
    │   │   ├── dashboard/
    │   │   │   ├── [role]/
    │   │   │   │   ├── billing/
    │   │   │   │   │   └── page.tsx
    │   │   │   │   └── page.tsx
    │   │   │   └── layout.tsx
    │   │   ├── api/
    │   │   │   └── auth/
    │   │   │       └── [...nextauth]/
    │   │   │           └── route.ts
    │   │   └── layout.tsx
    │   ├── components/
    │   │   ├── auth/
    │   │   │   ├── SignUpForm.tsx
    │   │   │   └── LoginForm.tsx
    │   │   └── ui/
    │   │       └── RoleSelect.tsx
    │   ├── lib/
    │   │   ├── graphql/
    │   │   │   ├── mutations.ts
    │   │   │   └── queries.ts
    │   │   └── apollo-client.ts
    │   └── providers.tsx
    ├── Dockerfile
    └── package.json