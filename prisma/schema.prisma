// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Roles Enum
enum Role {
  // Admin Users
  SUPER_USER
  ADMIN
  MODERATOR
  
  // Web Users
  DEV_COMPANY
  AGENCY
  STANDARD_USER
}

// Subscription Plans Enum
enum SubscriptionPlan {
  FREE
  STARTER
  PRO
}

// Subscription Status Enum
enum SubscriptionStatus {
  ACTIVE
  INACTIVE
  CANCELLED
  PAST_DUE
  TRIALING
}

// OAuth Provider Enum
enum OAuthProvider {
  GOOGLE
  FACEBOOK
}

// User Model
model User {
  id                Int       @id @default(autoincrement())
  email             String    @unique
  username          String?   @unique
  mobileNumber      String?   @unique
  password          String?   // Nullable for OAuth users
  firstName         String?
  lastName          String?
  profilePicture    String?
  
  // Role and Status
  role              Role      @default(STANDARD_USER)
  isActive          Boolean   @default(true)
  isEmailVerified   <PERSON>olean   @default(false)
  
  // OAuth Fields
  oauthProvider     OAuthProvider?
  oauthId           String?
  
  // Password Reset Fields
  resetToken        String?
  resetTokenExpiry  DateTime?
  
  // Email Verification Fields
  emailVerificationToken String?
  emailVerificationExpiry DateTime?
  
  // Timestamps
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  lastLoginAt       DateTime?
  
  // Relations
  subscription      Subscription?
  payments          Payment[]
  createdUsers      User[]    @relation("AdminCreatedUsers")
  createdBy         User?     @relation("AdminCreatedUsers", fields: [createdById], references: [id])
  createdById       Int?
  
  @@map("users")
}

// Subscription Model
model Subscription {
  id                String              @id @default(cuid())
  userId            Int                 @unique
  plan              SubscriptionPlan    @default(FREE)
  status            SubscriptionStatus  @default(ACTIVE)
  
  // Stripe Fields
  stripeCustomerId      String?
  stripeSubscriptionId  String?
  stripePriceId         String?
  
  // Billing Cycle
  currentPeriodStart    DateTime?
  currentPeriodEnd      DateTime?
  cancelAtPeriodEnd     Boolean           @default(false)
  canceledAt            DateTime?
  
  // Trial
  trialStart            DateTime?
  trialEnd              DateTime?
  
  // Timestamps
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt
  
  // Relations
  user                  User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("subscriptions")
}

// Payment Model
model Payment {
  id                    String    @id @default(cuid())
  userId                Int
  amount                Int       // Amount in cents
  currency              String    @default("usd")
  status                String    // succeeded, pending, failed
  
  // Stripe Fields
  stripePaymentIntentId String?
  stripeChargeId        String?
  
  // Payment Details
  description           String?
  receiptUrl            String?
  
  // Timestamps
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  
  // Relations
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("payments")
}

// Session Model for JWT Token Management
model Session {
  id            String    @id @default(cuid())
  userId        Int
  refreshToken  String    @unique
  expiresAt     DateTime
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  @@map("sessions")
}

// Admin Activity Log
model AdminActivityLog {
  id          Int      @id @default(autoincrement())
  adminId     Int
  action      String   // CREATE_USER, UPDATE_USER, DELETE_USER, etc.
  targetId    Int?     // ID of the target user/resource
  details     Json?    // Additional details about the action
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime @default(now())
  
  @@map("admin_activity_logs")
}
