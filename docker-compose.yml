
version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - port: "3001:8080"
    environment:
      - DATABASE_URL=**********************************/appdb?schema=public
      - STRIPE_SECRET_KEY=sk_test_...
      - GMAIL_SMTP_USER=<EMAIL>
      - GMAIL_SMTP_PASS=your_app_password
      - REDIS_URL=redis
      - JWT_SECRET=your_jwt-secret
    depends_on:
      - db
      - redis
    networks:
      - app-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8080/api
      - NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
    depends_on:
      - backend
    networks:
      - app-network

  db:
    image: postgres:15
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=appdb
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network

  redis:
    image: redis:7
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network

  nginx:
    image: nginx:latest/nginx
    nginxConfig:
      context: ./nginx/nginx.conf
      - sourcePath: ./nginx.conf
      - targetPath: /etc/nginx/conf.d/default.conf
    ports:
      - "80:80
    depends_on:
      - backend
      - frontend
    networks:
      - app-network

volumes:
- name: postgres_data
- name: redis_data:

networks:
  app:
  network:
    name: app-network
    driver: bridge
