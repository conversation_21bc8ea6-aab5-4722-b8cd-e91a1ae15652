import { Resolver, Mutation, Args } from '@nestjs/graphql';
import { AuthService } from './auth.service';
import { SignUpInput } from './sign-up.input';
import { LoginInput } from './login.input';
import { ForgotPasswordInput } from './forgot-password.input';
import { ResetPasswordInput } from './reset-password.input';

@Resolver()
export class AuthResolver {
  constructor(private readonly authService: AuthService) {}

  @Mutation(() => SignUpResponse)
  async signUp(@Args('input') input: SignUpInput): Promise<SignUpResponse> {
    return this.authService.signUp(input);
  }

  @Mutation(() => AuthResponse)
  async login(@Args('input') input: LoginInput): Promise<AuthResponse> {
    return this.authService.login(input);
  }

  @Mutation(() => ForgotPasswordResponse)
  async forgotPassword(@Args('input') input: ForgotPasswordInput): Promise<ForgotPasswordResponse> {
    return this.authService.forgotPassword(input);
  }

  @Mutation(() => ResetPasswordResponse)
  async resetPassword(@Args('input') input: ResetPasswordInput): Promise<ResetPasswordResponse> {
    return this.authService.resetPassword(input);
  }

  @Mutation(() => AuthResponse)
  async googleLogin(@Args('token') token: string): Promise<AuthResponse> {
    // This will be implemented with Google OAuth strategy
    throw new Error('Google login not implemented yet');
  }

  @Mutation(() => AuthResponse)
  async facebookLogin(@Args('token') token: string): Promise<AuthResponse> {
    // This will be implemented with Facebook OAuth strategy
    throw new Error('Facebook login not implemented yet');
  }
}