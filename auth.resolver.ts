import { Resolver, Mutation, Args } from '@nestjs/graphql';
import { AuthService } from './auth.service';
import { SignUpInput } from './dto/sign-up.input';
import { LoginInput } from './dto/login.input';
import { ForgotPasswordInput } from './dto/forgot-password.input';
import { ResetPasswordInput } from './dto/reset-password.input';
import { AuthResponse } from './dto/auth-response';
import { UseGuards } from '@nestjs/common';
import { GqlAuthGuard } from './guards/gql-auth.guard';

@Resolver()
export class AuthResolver {
  constructor(private authService: AuthService) {}

  @Mutation(() => AuthResponse)
  async signUp(@Args('input') input: SignUpInput) {
    return this.authService.signUp(input);
  }

  @Mutation(() => AuthResponse)
  async login(@Args('input') input: LoginInput) {
    return this.authService.login(input);
  }

  @Mutation(() => AuthResponse)
  async forgotPassword(@Args('input') input: ForgotPasswordInput) {
    return this.authService.forgotPassword(input);
  }

  @Mutation(() => AuthResponse)
  async resetPassword(@Args('input') input: ResetPasswordInput) {
    return this.authService.resetPassword(input);
  }

  @Mutation(() => AuthResponse)
  @UseGuards(GqlAuthGuard)
  async googleLogin(@Args('token') token: string) {
    // Handle Google OAuth token
    // This will be implemented with GoogleStrategy
    return this.authService.googleLogin({ /* Parsed token data */ });
  }

  @Mutation(() => AuthResponse)
  @UseGuards(GqlAuthGuard)
  async facebookLogin(@Args('token') token: string) {
    // Handle Facebook OAuth token
    // This will be implemented with FacebookStrategy
    return this.authService.facebookLogin({ /* Parsed token data */ });
  }
}