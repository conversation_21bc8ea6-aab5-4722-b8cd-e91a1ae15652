├── .env
├── .gitignore
├── README.md
├── docker-compose.yml
├── nginx/
│   └── nginx.conf
├── backend/
│   ├── src/
│   │   ├── auth/
│   │   │   ├── strategies/
│   │   │   │   ├── local.strategy.ts
│   │   │   │   ├── jwt.strategy.ts
│   │   │   │   ├── google.strategy.ts
│   │   │   │   └── facebook.strategy.ts
│   │   │   ├── guards/
│   │   │   │   └── roles.guard.ts
│   │   │   ├── dto/
│   │   │   │   ├── sign-up.input.ts
│   │   │   │   ├── login.input.ts
│   │   │   │   ├── forgot-password.input.ts
│   │   │   │   └── reset-password.input.ts
│   │   │   ├── auth.module.ts
│   │   │   ├── auth.resolver.ts
│   │   │   └── auth.service.ts
│   │   ├── user/
│   │   │   ├── user.module.ts
│   │   │   ├── user.resolver.ts
│   │   │   └── user.service.ts
│   │   ├── payment/
│   │   │   ├── stripe/
│   │   │   │   ├── webhook.controller.ts
│   │   │   │   ├── stripe.service.ts
│   │   │   │   └── stripe.module.ts
│   │   │   ├── dto/
│   │   │   │   ├── create-session.input.ts
│   │   │   │   └── cancel-subscription.input.ts
│   │   │   ├── payment.module.ts
│   │   │   ├── payment.resolver.ts
│   │   │   └── payment.service.ts
│   │   ├── mail/
│   │   │   ├── mail.module.ts
│   │   │   └── mail.service.ts
│   │   ├── prisma/
│   │   │   ├── prisma.service.ts
│   │   │   └── prisma.module.ts
│   │   ├── config/
│   │   │   ├── config.module.ts
│   │   │   └── config.service.ts
│   │   ├── common/
│   │   │   ├── decorators/
│   │   │   │   └── roles.decorator.ts
│   │   │   ├── exceptions/
│   │   │   │   └── http-exception.filter.ts
│   │   │   └── utils/
│   │   │       └── bcrypt.util.ts
│   │   ├── app.module.ts
│   │   └── main.ts
│   ├── prisma/
│   │   └── schema.prisma
│   ├── tests/
│   │   ├── auth/
│   │   │   └── auth.service.spec.ts
│   │   ├── user/
│   │   │   └── user.service.spec.ts
│   │   ├── payment/
│   │   │   └── payment.service.spec.ts
│   ├── Dockerfile
│   └── package.json
└── frontend/
    ├── src/
    │   ├── app/
    │   │   ├── (auth)/
    │   │   │   ├── sign-up/
    │   │   │   │   └── page.tsx
    │   │   │   ├── login/
    │   │   │   │   └── page.tsx
    │   │   │   ├── forgot-password/
    │   │   │   │   └── page.tsx
    │   │   │   └── reset-password/
    │   │   │       └── page.tsx
    │   │   ├── dashboard/
    │   │   │   ├── [role]/
    │   │   │   │   ├── billing/
    │   │   │   │   │   └── page.tsx
    │   │   │   │   └── page.tsx
    │   │   │   └── layout.tsx
    │   │   ├── api/
    │   │   │   └── auth/
    │   │   │       └── [...nextauth]/
    │   │   │           └── route.ts
    │   │   ├── layout.tsx
    │   │   └── middleware.ts
    │   ├── components/
    │   │   ├── auth/
    │   │   │   ├── SignUpForm.tsx
    │   │   │   ├── LoginForm.tsx
    │   │   │   ├── ForgotPasswordForm.tsx
    │   │   │   └── ResetPasswordForm.tsx
    │   │   ├── billing/
    │   │   │   ├── SubscriptionCard.tsx
    │   │   │   ├── PaymentHistoryTable.tsx
    │   │   │   └── PaymentMethodForm.tsx
    │   │   └── ui/
    │   │       └── RoleSelect.tsx
    │   ├── lib/
    │   │   ├── graphql/
    │   │   │   ├── mutations.ts
    │   │   │   └── queries.ts
    │   │   ├── apollo-client.ts
    │   │   └── stripe.ts
    │   ├── __tests__/
    │   │   ├── components/
    │   │   │   ├── SignUpForm.test.tsx
    │   │   │   ├── LoginForm.test.tsx
    │   │   │   └── RoleSelect.test.tsx
    │   │   └── pages/
    │   │       └── dashboard.test.tsx
    │   └── providers.tsx
    ├── Dockerfile
    └── package.json