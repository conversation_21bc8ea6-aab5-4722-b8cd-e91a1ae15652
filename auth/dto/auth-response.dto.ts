import { ObjectType, Field } from '@nestjs/graphql';
import { User } from '../../user/entities/user.entity';

@ObjectType()
export class AuthResponse {
  @Field()
  accessToken: string;

  @Field()
  refreshToken: string;

  @Field(() => User)
  user: User;

  @Field()
  message: string;

  @Field()
  redirectUrl: string;
}

@ObjectType()
export class SignUpResponse {
  @Field()
  message: string;

  @Field()
  redirectUrl: string;
}

@ObjectType()
export class ForgotPasswordResponse {
  @Field()
  message: string;
}

@ObjectType()
export class ResetPasswordResponse {
  @Field()
  message: string;

  @Field()
  redirectUrl: string;
}
