import { ObjectType, Field, Int, registerEnumType } from '@nestjs/graphql';
import { Role, OAuthProvider } from '@prisma/client';

registerEnumType(Role, { name: 'Role' });
registerEnumType(OAuthProvider, { name: '<PERSON>Auth<PERSON>rovider' });

@ObjectType()
export class User {
  @Field(() => Int)
  id: number;

  @Field()
  email: string;

  @Field({ nullable: true })
  username?: string;

  @Field({ nullable: true })
  mobileNumber?: string;

  @Field({ nullable: true })
  firstName?: string;

  @Field({ nullable: true })
  lastName?: string;

  @Field({ nullable: true })
  profilePicture?: string;

  @Field(() => Role)
  role: Role;

  @Field()
  isActive: boolean;

  @Field()
  isEmailVerified: boolean;

  @Field(() => OAuthProvider, { nullable: true })
  oauthProvider?: OAuthProvider;

  @Field({ nullable: true })
  oauthId?: string;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;

  @Field({ nullable: true })
  lastLoginAt?: Date;

  @Field(() => Int, { nullable: true })
  createdById?: number;
}
