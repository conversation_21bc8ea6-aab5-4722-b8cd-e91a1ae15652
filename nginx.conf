
server {
    listen 80;

    location / {
        proxy_pass http://frontend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $local_scheme;
    }

    location /api/ {
        proxy_pass http://backend:8080/api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Rate Limiting for Forgot Password
    limit_req_zone $binary_remote_addr zone=forgot_password:10m rate=5r/m;
    location /api/forgot/password {
        limit_req zone=forgot_password burst=10;
        proxy_pass http://backend:8080/api;
    }
}
